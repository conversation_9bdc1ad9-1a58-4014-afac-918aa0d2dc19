package com.aionemu.gameserver.instance.handlers;

import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.RechargerService;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * Recharger Instance Handler - Creates clean instances without monsters
 * 
 * This handler ensures that Recharger instances are clean sanctuaries
 * without any of the original dungeon monsters or NPCs.
 * Only the healing NPC will be spawned by the RechargerService.
 * 
 * <AUTHOR>
 */
public class RechargerInstanceHandler extends GeneralInstanceHandler {

    public RechargerInstanceHandler(WorldMapInstance instance) {
        super(instance);
    }

    @Override
    public void onInstanceCreate() {
        // Don't call super.onInstanceCreate() to prevent spawning original monsters
        // The RechargerService will handle spawning only the healing NPC
    }

    @Override
    public void onPlayerLogin(Player player) {
        // Allow normal player login handling
        super.onPlayerLogin(player);
    }

    @Override
    public void onPlayerLogout(Player player) {
        // Allow normal player logout handling
        super.onPlayerLogout(player);
    }

    @Override
    public void onEnterInstance(Player player) {
        // Set neutral state so different factions appear neutral to each other
        player.setCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.NEUTRAL_TO_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();

        // Allow normal enter handling
        super.onEnterInstance(player);
    }

    @Override
    public void onLeaveInstance(Player player) {
        // Remove neutral state when leaving the recharger instance
        player.unsetCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.NEUTRAL_TO_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();

        // Allow normal leave handling
        super.onLeaveInstance(player);
    }

    @Override
    public void onInstanceDestroy() {
        // Clean up spawned NPCs when instance is destroyed
        RechargerService.getInstance().onInstanceDestroy(instance.getInstanceId());
        super.onInstanceDestroy();
    }
}
